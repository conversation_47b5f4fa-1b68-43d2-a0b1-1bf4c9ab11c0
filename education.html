<!DOCTYPE html>
<html lang="en">
    
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="stylesheet" href="style.css">
    <title>Education - Miguel <PERSON></title>
</head>
<body>
    <header class="navbar">
        <div class="nav-container">
            <a href="index.html" class="logo" aria-label="<PERSON> V Tan - Home">
                <span class="logo-text">@leugimnat</span>
            </a>
            
            <nav class="nav-menu" role="navigation" aria-label="Main navigation">
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="index.html" class="nav-link">
                            <i class="fa-solid fa-house"></i>
                            <span>Home</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="education.html" class="nav-link active" aria-current="page">
                            <i class="fa-solid fa-graduation-cap"></i>
                            <span>Education</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="experience.html" class="nav-link">
                            <i class="fa-solid fa-briefcase"></i>
                            <span>Experience</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="portfolio.html" class="nav-link">
                            <i class="fa-solid fa-folder-open"></i>
                            <span>Portfolio</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="index.html#contact" class="nav-link">
                            <i class="fa-solid fa-envelope"></i>
                            <span>Contact</span>
                        </a>
                    </li>
                </ul>
            </nav>
            
            <button type="button" class="mobile-menu-toggle" aria-label="Toggle mobile menu" aria-expanded="false">
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
            </button>
        </div>
    </header>

    <main class="education-page">
        <div class="education-hero">
            <div class="hero-content">
                <h1>My Educational <span>Journey</span></h1>
                <p class="hero-subtitle">After I did NAT and NCAE, I realized I was within range to a lot of opportunities in college. My mother wanted me to be a doctor, but I wanted to be an IT, so I chose Bachelor of Science and Information Technology.</p>
                <p class="hero-subtitle">The first few years were rough, it was learning java, object-oriented-programming, databases, theories and etc. However it quicly took up pace during 3rd year and 4th year wherein I was able to apply what I've learned into actualy projects.</p>
                <p class="hero-subtitle">Throughout my academic and internship experience, I’ve built strong foundations in Python, JavaScript, MySQL, and network systems, which I applied in both backend and frontend development projects. One of my most significant contributions was to our university capstone project, “RequeXU: An Adaptive Web Application for Streamlined Multi-Campus Work Order and Dispatch Management,” which was successfully deployed in a production environment for our university's Physical Plant Office. This involved developing clean, scalable APIs, integrating external services, and managing real-time data.</p>
                <p class="hero-subtitle">I also interned with M. Montesclaros Holdings Inc., where I worked in the technical IT team and further developed my ability to communicate with stakeholders, support system improvements, and apply logic to resolve technical challenges. These experiences helped me build a strong foundation in agile planning, backlog management, testing, and pair programming.</p>
            </div>
                <div class="education-gallery">
                <div class="container">
                    <h2 class="section-title">My <span>Gallery</span></h2>
                    <p class="gallery-subtitle">Snippets from my Projects found bellow.</p>

                    <div class="gallery-grid">
                        <div class="gallery-item" data-category="university">
                            <div class="gallery-image">
                                <img src="images/university/univ1.jpeg" alt="University graduation ceremony with my friends." loading="lazy">
                                <div class="gallery-overlay">
                                    <div class="gallery-info">
                                        <h4>Graduation Day Picture with my Friends.</h4>
                                        <p>Xavier University - Ateneo de Cagayan</p>
                                        <span class="gallery-date">2025</span>
                                    </div>
                                    <button type="button" class="gallery-expand" aria-label="View full image">
                                        <i class="fa-solid fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="gallery-item" data-category="university">
                            <div class="gallery-image">
                                <img src="images/university/univ2.jpeg" alt="University graduation ceremony with my friends back photo." loading="lazy">
                                <div class="gallery-overlay">
                                    <div class="gallery-info">
                                        <h4>Graduation Day Picture with my Friends.</h4>
                                        <p>Xavier University - Ateneo de Cagayan</p>
                                        <span class="gallery-date">2025</span>
                                    </div>
                                    <button type="button" class="gallery-expand" aria-label="View full image">
                                        <i class="fa-solid fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="gallery-item" data-category="university">
                            <div class="gallery-image">
                                <img src="images/university/univ3.jpeg" alt="University graduation ceremony with my friends church backdrop" loading="lazy">
                                <div class="gallery-overlay">
                                    <div class="gallery-info">
                                        <h4>Graduation Day Picture with my Friends.</h4>
                                        <p>Xavier University - Ateneo de Cagayan</p>
                                        <span class="gallery-date">2025</span>
                                    </div>
                                    <button type="button" class="gallery-expand" aria-label="View full image">
                                        <i class="fa-solid fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="gallery-item" data-category="projects">
                            <div class="gallery-image">
                                <img src="images/projects/capstone_meeting.jpg" alt="Capstone project picture with stakeholders" loading="lazy">
                                <div class="gallery-overlay">
                                    <div class="gallery-info">
                                        <h4>Capstone Project Picture w/ Stakeholders</h4>
                                        <span class="gallery-date">2025</span>
                                    </div>
                                    <button type="button" class="gallery-expand" aria-label="View full image">
                                        <i class="fa-solid fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="gallery-item" data-category="projects">
                            <div class="gallery-image">
                                <img src="images/projects/capstone_meeting1.jpg" alt="Capstone project meeting with stakeholders" loading="lazy">
                                <div class="gallery-overlay">
                                    <div class="gallery-info">
                                        <h4>Capstone Project Meeting w/ Stakeholders</h4>
                                        <p>Final year project meeting.</p>
                                        <span class="gallery-date">2025</span>
                                    </div>
                                    <button type="button" class="gallery-expand" aria-label="View full image">
                                        <i class="fa-solid fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="gallery-item" data-category="projects">
                            <div class="gallery-image">
                                <img src="images\projects\capstone_meeting2.jpg" alt="Capstone project meeting with stakeholders" loading="lazy">
                                <div class="gallery-overlay">
                                    <div class="gallery-info">
                                        <h4>Capstone Project Meeting w/ Stakeholders</h4>
                                        <p>Final year project meeting.</p>
                                        <span class="gallery-date">2025</span>
                                    </div>
                                    <button type="button" class="gallery-expand" aria-label="View full image">
                                        <i class="fa-solid fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="gallery-item" data-category="projects">
                            <div class="gallery-image">
                                <img src="images/projects/field_work.jpg" alt="Field work during OJT" loading="lazy">
                                <div class="gallery-overlay">
                                    <div class="gallery-info">
                                        <h4>OJT Field Work w/ Supervisor</h4>
                                        <p>3rd Month of OJT.</p>
                                        <span class="gallery-date">2024</span>
                                    </div>
                                    <button type="button" class="gallery-expand" aria-label="View full image">
                                        <i class="fa-solid fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="gallery-item" data-category="projects">
                            <div class="gallery-image">
                                <img src="images/projects/office.jpg" alt="M.Montesclaros Holdings Incorporated Office" loading="lazy">
                                <div class="gallery-overlay">
                                    <div class="gallery-info">
                                        <h4>M.MontesClaros Holdings Incorporated Office</h4>
                                        <p>First time coming from OJT</p>
                                        <span class="gallery-date">2024</span>
                                    </div>
                                    <button type="button" class="gallery-expand" aria-label="View full image">
                                        <i class="fa-solid fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="gallery-item" data-category="achievements">
                            <div class="gallery-image">
                                <img src="images\achievements\best presenter_information.jpg" alt="Academic achievement award for being best presenter in information technology" loading="lazy">
                                <div class="gallery-overlay">
                                    <div class="gallery-info">
                                        <h4>Best Presenter in Information Technology</h4>
                                        <p>Awarded for outstanding capstone project demonstrating innovation and technical excellence.</p>
                                        <span class="gallery-date">2025</span>
                                    </div>
                                    <button type="button" class="gallery-expand" aria-label="View full image">
                                        <i class="fa-solid fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="gallery-item" data-category="achievements">
                            <div class="gallery-image">
                                <img src="images\achievements\best_presenter.jpg" alt="Academic achievement award for being best presenter in information technology" loading="lazy">
                                <div class="gallery-overlay">
                                    <div class="gallery-info">
                                        <h4>Best Presenter in Information Technology</h4>
                                        <p>Awarded for outstanding capstone project demonstrating innovation and technical excellence.</p>
                                        <span class="gallery-date">2025</span>
                                    </div>
                                    <button type="button" class="gallery-expand" aria-label="View full image">
                                        <i class="fa-solid fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="gallery-item" data-category="achievements">
                            <div class="gallery-image">
                                <img src="images\achievements\capstone1.jpg" alt="Captsone Defended" loading="lazy">
                                <div class="gallery-overlay">
                                    <div class="gallery-info">
                                        <h4>Capstone Defense</h4>
                                        <p>RequeXU: An Adaptive Web Application for Streamlined Multi-Campus Work Order and Dispatch Management</p>
                                        <span class="gallery-date">2025</span>
                                    </div>
                                    <button type="button" class="gallery-expand" aria-label="View full image">
                                        <i class="fa-solid fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="gallery-item" data-category="achievements">
                            <div class="gallery-image">
                                <img src="images\achievements\capstone2.jpg" alt="Capstone Defense" loading="lazy">
                                <div class="gallery-overlay">
                                    <div class="gallery-info">
                                        <h4>Capstone Defense</h4>
                                        <p>RequeXU: An Adaptive Web Application for Streamlined Multi-Campus Work Order and Dispatch Management</p>
                                        <span class="gallery-date">2025</span>
                                    </div>
                                    <button type="button" class="gallery-expand" aria-label="View full image">
                                        <i class="fa-solid fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="gallery-item" data-category="achievements">
                            <div class="gallery-image">
                                <img src="images\achievements\cert_completion.jpg" alt="Certificate of Completion for On the Job Training" loading="lazy">
                                <div class="gallery-overlay">
                                    <div class="gallery-info">
                                        <h4>Certificate of Completion for On the Job Training</h4>
                                        <p>M.Montesclaros Holdings Incorporated</p>
                                        <span class="gallery-date">2024</span>
                                    </div>
                                    <button type="button" class="gallery-expand" aria-label="View full image">
                                        <i class="fa-solid fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="gallery-item" data-category="achievements">
                            <div class="gallery-image">
                                <img src="images\achievements\cert_of_recognition.jpg" alt="Certificate of Recognition for Best Capstone Presenter in Information Technology" loading="lazy">
                                <div class="gallery-overlay">
                                    <div class="gallery-info">
                                        <h4>Certificate of Recognition for Best Capstone Presenter in Information Technology</h4>
                                        <p>Awarded for outstanding capstone project demonstrating innovation and technical excellence.</p>
                                        <span class="gallery-date">2025</span>
                                    </div>
                                    <button type="button" class="gallery-expand" aria-label="View full image">
                                        <i class="fa-solid fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="gallery-item" data-category="achievements">
                            <div class="gallery-image">
                                <img src="images\achievements\hardware_troubleshooting.jpg" alt="Hardware Troubleshooting award & certificate of presentation" loading="lazy">
                                <div class="gallery-overlay">
                                    <div class="gallery-info">
                                        <h4>Hardware Troubleshooting Presentation</h4>
                                        <p>Awarded for outstanding capstone project demonstrating innovation and technical excellence.</p>
                                        <span class="gallery-date">2024</span>
                                    </div>
                                    <button type="button" class="gallery-expand" aria-label="View full image">
                                        <i class="fa-solid fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="gallery-filters">
                        <button type="button" class="filter-btn active" data-filter="all">All Photos</button>
                        <button type="button" class="filter-btn" data-filter="university">University Life</button>
                        <button type="button" class="filter-btn" data-filter="projects">Projects</button>
                        <button type="button" class="filter-btn" data-filter="achievements">Achievements</button>
                    </div>
                </div>
            </div>
        </div>
        </section>

        <section class="education-timeline">
            <div class="container">
                <h2 class="section-title">Academic <span>Background</span></h2>
                
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker">
                            <i class="fa-solid fa-graduation-cap"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-header">
                                <h3>Bachelor of Science in Information Technology</h3>
                                <span class="timeline-date">2021 - 2025</span>
                            </div>
                            <h4 class="institution">Xavier University - Ateneo de Cagayan</h4>
                            <p class="location">Cagayan de Oro City, Philippines</p>
                            <div class="education-details">
                                <h5>Key Coursework:</h5>
                                <ul class="coursework-list">
                                    <li>Data Structures and Algorithms</li>
                                    <li>Database Systems and Information Management</li>
                                    <li>Object-Oriented Programming</li>
                                    <li>Integrative Programing and Technologies</li>
                                    <li>Web Development and Development</li>
                                    <li>Networking Administration</li>
                                    <li>Software Engineering</li>
                                    <li>Mobile Application Development</li>
                                    <li>Systems Integration Architecture</li>
                                    <li>Systems Administration and Maintenance</li>
                                    <li>Introduction to Cloud Computing</li>
                                </ul>
                            </div>
                            <div class="achievements">
                                <h5>Notable Achievements:</h5>
                                <ul class="achievement-list">
                                    <li>Dean's List recognition for academic excellence</li>
                                    <li>Completed capstone project on progressive web-based management system</li>
                                    <li>Certificate of Recognition for Best Capstone Presenter in Information Technology</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-item">
                        <div class="timeline-marker">
                            <i class="fa-solid fa-school"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-header">
                                <h3>Senior High School - STEM Track</h3>
                                <span class="timeline-date">2019 - 2021</span>
                            </div>
                            <h4 class="institution">Xavier University - Ateneo de Cagayan</h4>
                            <p class="location">Cagayan de Oro City, Philippines</p>
                            <div class="education-details">
                                <h5>Specialized Subjects:</h5>
                                <ul class="coursework-list">
                                    <li>Computer Programming (Introduction to coding)</li>
                                    <li>Research and Statistics</li>
                                    <li>Advanced Mathematics</li>
                                    <li>Physics and Chemistry</li>
                                    <li>ICT and Digital Literacy</li>
                                </ul>
                            </div>
                            <div class="achievements">
                                <h5>Highlights:</h5>
                                <ul class="achievement-list">
                                    <li>First exposure to programming through Minecraft coding exercises</li>
                                    <li>Developed strong analytical and problem-solving skills</li>
                                    <li>Participated in science and technology competitions</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="certifications">
            <div class="container">
                <h2 class="section-title">Certifications & <span>Learning</span></h2>
                <div class="cert-grid">
                    <div class="cert-card">
                        <div class="cert-icon">
                            <i class="fa-brands fa-html5"></i>
                        </div>
                        <h3>Web Development</h3>
                        <p>Self-taught HTML, CSS, JavaScript, and modern frameworks</p>
                        <span class="cert-status">Ongoing</span>
                    </div>
                    
                    <div class="cert-card">
                        <div class="cert-icon">
                            <i class="fa-solid fa-diagram-project"></i>
                        </div>
                        <h3>Google Project Management: Specialization</h3>
                        <p>https://www.coursera.org/account/accomplishments/specialization/VHFCN6QLLZ3K</p>
                        <span class="cert-status">Completed</span>
                    </div>
                    
                    <div class="cert-card">
                        <div class="cert-icon">
                            <i class="fa-brands fa-react"></i>
                        </div>
                        <h3>MERN Stack Development</h3>
                        <p>MongoDB, Express.js, React, Node.js full-stack development</p>
                        <span class="cert-status">In Progress</span>
                    </div>
                    
                    <div class="cert-card">
                        <div class="cert-icon">
                            <i class="fa-solid fa-network-wired"></i>
                        </div>
                        <h3> CCNA: Switching, Routing, and Wireless Essentials </h3>
                        <p>https://www.credly.com/badges/52dc87e1-2e8b-4cf3-a8d8-285c93bcf223/public_url</p>
                        <span class="cert-status">Completed</span>
                    </div>

                    <div class="cert-card">
                        <div class="cert-icon">
                            <i class="fa-solid fa-network-wired"></i>
                        </div>
                        <h3>Certified Ethical Hacker (CEH) </h3>
                        <p>https://www.credly.com/badges/abb594d7-dbd2-43bb-bd4a-b21fb48e390b/public_url </p>
                        <span class="cert-status">Completed</span>
                    </div>
                </div>
            </div>
        </section>

        <section class="skills-acquired">
            <div class="container">
                <h2 class="section-title">Skills <span>Acquired</span></h2>
                <div class="skills-categories">
                    <div class="skill-category">
                        <h3><i class="fa-solid fa-code"></i> Programming Languages</h3>
                        <div class="skill-tags">
                            <span class="skill-tag">JavaScript</span>
                            <span class="skill-tag">Python</span>
                            <span class="skill-tag">Java</span>
                            <span class="skill-tag">C++</span>
                            <span class="skill-tag">PHP</span>
                        </div>
                    </div>

                    <div class="skill-category">
                        <h3><i class="fa-solid fa-globe"></i> Web Technologies</h3>
                        <div class="skill-tags">
                            <span class="skill-tag">HTML5</span>
                            <span class="skill-tag">CSS3</span>
                            <span class="skill-tag">React</span>
                            <span class="skill-tag">Node.js</span>
                            <span class="skill-tag">Express.js</span>
                        </div>
                    </div>

                    <div class="skill-category">
                        <h3><i class="fa-solid fa-database"></i> Database & Tools</h3>
                        <div class="skill-tags">
                            <span class="skill-tag">MySQL</span>
                            <span class="skill-tag">MongoDB</span>
                            <span class="skill-tag">Git</span>
                            <span class="skill-tag">VS Code</span>
                            <span class="skill-tag">Figma</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="site-footer">
        <div class="footer-inner">
            <div class="footer-main">
                <div class="footer-brand">
                    <h3 class="footer-logo">@leugimnat</h3>
                    <p class="footer-name">&copy; <span id="year"></span> Miguel Antonio V Tan</p>
                    <p class="footer-status">
                        <span class="status-indicator"></span>
                        Open to Front-End / Full-Stack Roles / Internship Opportunities
                    </p>
                </div>

                <div class="footer-links">
                    <div class="footer-contact">
                        <h4>Get In Touch</h4>
                        <div class="contact-info">
                            <a href="mailto:<EMAIL>" class="contact-email">
                                <i class="fa-solid fa-envelope"></i>
                                <EMAIL>
                            </a>
                            <div class="contact-location">
                                <i class="fa-solid fa-location-dot"></i>
                                <span>Cagayan de Oro City, Philippines</span>
                            </div>
                        </div>
                    </div>

                    <div class="footer-social">
                        <h4>Connect</h4>
                        <div class="social-links">
                            <a href="https://github.com/leugimnat" aria-label="GitHub Profile" title="GitHub">
                                <i class="fa-brands fa-github"></i>
                                <span>GitHub</span>
                            </a>
                            <a href="https://www.linkedin.com/in/miguel-antonio-tan-945499255/" aria-label="LinkedIn Profile" title="LinkedIn">
                                <i class="fa-brands fa-linkedin"></i>
                                <span>LinkedIn</span>
                            </a>
                            <a href="https://www.facebook.com/zeuqzavleugimnat/" aria-label="Facebook Profile" title="Facebook">
                                <i class="fa-brands fa-facebook"></i>
                                <span>Facebook</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="footer-tech">
                    <span class="tech-label">Tech Stack:</span>
                    <div class="tech-stack">
                        <span class="tech-item">HTML</span>
                        <span class="tech-item">CSS</span>
                        <span class="tech-item">JavaScript</span>
                    </div>
                </div>
                <p class="footer-note">
                    <i class="fa-solid fa-heart"></i>
                    Crafted with care • Accessible & responsive • Last updated Aug 2025
                </p>
            </div>
        </div>
    </footer>

    <!-- Gallery Modal -->
    <div class="gallery-modal" id="galleryModal">
        <div class="modal-content">
            <img class="modal-image" id="modalImage" src="" alt="">
            <button type="button" class="modal-close" id="modalClose" aria-label="Close modal">
                <i class="fa-solid fa-times"></i>
            </button>
        </div>
    </div>

    <script>
        // Update year
        document.getElementById('year').textContent = new Date().getFullYear();

        // Mobile menu functionality
        const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
        const navMenu = document.querySelector('.nav-menu');
        const navLinks = document.querySelectorAll('.nav-link');
        const navbar = document.querySelector('.navbar');

        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', () => {
            mobileMenuToggle.classList.toggle('active');
            navMenu.classList.toggle('active');

            // Update aria-expanded attribute
            const isExpanded = navMenu.classList.contains('active');
            mobileMenuToggle.setAttribute('aria-expanded', isExpanded);
        });

        // Close mobile menu when clicking on nav links
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                mobileMenuToggle.classList.remove('active');
                navMenu.classList.remove('active');
                mobileMenuToggle.setAttribute('aria-expanded', 'false');
            });
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!navMenu.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
                mobileMenuToggle.classList.remove('active');
                navMenu.classList.remove('active');
                mobileMenuToggle.setAttribute('aria-expanded', 'false');
            }
        });

        // Navbar scroll effect
        window.addEventListener('scroll', () => {
            if (window.scrollY > 100) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Gallery functionality
        const filterBtns = document.querySelectorAll('.filter-btn');
        const galleryItems = document.querySelectorAll('.gallery-item');
        const galleryModal = document.getElementById('galleryModal');
        const modalImage = document.getElementById('modalImage');
        const modalClose = document.getElementById('modalClose');
        const expandBtns = document.querySelectorAll('.gallery-expand');

        console.log('Gallery elements found:', {
            filterBtns: filterBtns.length,
            galleryItems: galleryItems.length,
            expandBtns: expandBtns.length
        });

        // Filter functionality
        filterBtns.forEach((btn, index) => {
            console.log(`Setting up filter button ${index}:`, btn.textContent, btn.getAttribute('data-filter'));

            btn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                console.log('Filter clicked:', btn.getAttribute('data-filter'));

                // Remove active class from all buttons
                filterBtns.forEach(b => b.classList.remove('active'));
                // Add active class to clicked button
                btn.classList.add('active');

                const filter = btn.getAttribute('data-filter');
                console.log('Filtering by:', filter);

                galleryItems.forEach((item, itemIndex) => {
                    const category = item.getAttribute('data-category');
                    console.log(`Item ${itemIndex} category:`, category, 'Filter:', filter);

                    if (filter === 'all' || category === filter) {
                        item.style.display = 'block';
                        item.style.opacity = '1';
                        item.style.transform = 'scale(1)';
                    } else {
                        item.style.opacity = '0';
                        item.style.transform = 'scale(0.8)';
                        setTimeout(() => {
                            if (item.style.opacity === '0') {
                                item.style.display = 'none';
                            }
                        }, 300);
                    }
                });
            });
        });

        // Add smooth transitions to gallery items
        galleryItems.forEach(item => {
            item.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        });

        // Modal functionality
        expandBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const img = btn.closest('.gallery-item').querySelector('img');
                modalImage.src = img.src;
                modalImage.alt = img.alt;
                galleryModal.classList.add('active');
                document.body.style.overflow = 'hidden';
            });
        });

        // Close modal
        if (modalClose) {
            modalClose.addEventListener('click', () => {
                galleryModal.classList.remove('active');
                document.body.style.overflow = 'auto';
            });
        }

        // Close modal when clicking outside
        if (galleryModal) {
            galleryModal.addEventListener('click', (e) => {
                if (e.target === galleryModal) {
                    galleryModal.classList.remove('active');
                    document.body.style.overflow = 'auto';
                }
            });
        }

        // Close modal with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && galleryModal && galleryModal.classList.contains('active')) {
                galleryModal.classList.remove('active');
                document.body.style.overflow = 'auto';
            }
        });
    </script>
</body>
</html>
