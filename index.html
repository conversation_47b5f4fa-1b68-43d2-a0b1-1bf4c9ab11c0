<!DOCTYPE html>
<html lang="en">
    
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="stylesheet" href="style.css">
    <title>Resume Website</title>
</head>
<body>
    <header class="navbar">
        <div class="nav-container">
            <a href="index.html" class="logo" aria-label="<PERSON> Antonio V Tan - Home">
                <span class="logo-text">@leugimnat</span>
            </a>

            <nav class="nav-menu" role="navigation" aria-label="Main navigation">
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="#home" class="nav-link active" aria-current="page">
                            <i class="fa-solid fa-house"></i>
                            <span>Home</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="education.html" class="nav-link">
                            <i class="fa-solid fa-graduation-cap"></i>
                            <span>Education</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="experience.html" class="nav-link">
                            <i class="fa-solid fa-briefcase"></i>
                            <span>Experience</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#portfolio" class="nav-link">
                            <i class="fa-solid fa-folder-open"></i>
                            <span>Portfolio</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#contact" class="nav-link">
                            <i class="fa-solid fa-envelope"></i>
                            <span>Contact</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <button type="button" class="mobile-menu-toggle" aria-label="Toggle mobile menu" aria-expanded="false">
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
            </button>
        </div>
    </header>
    <section id="home" class="home">
        <div class="home-img">
            <img src="images/pt1.JPG" alt="Picture of Miguel Antonio V Tan in Taiwan." loading="lazy" decoding="async">
        </div>
        <div class="home-content">
            <div class="intro-section">
                <h1>Hey, I'm <span>Miguel</span>, nice to meet you!</h1>
                <h3 class="typing-text">I'm an aspiring <span></span></h3>
                <p class="intro-description">
                    BS Information Technology graduate from Xavier University - Ateneo de Cagayan with a passion for
                    networking, software development, web technologies, and project management.
                </p>
            </div>

            <div class="about-section">
                <h2>About <span>Me</span></h2>
                <div class="about-content">
                    <p>
                        Growing up, I've always had an obsession with technology. It started with a Sega Megadrive,
                        then a PlayStation Portable. Enamored with games, I soon discovered personal computers,
                        and from there my interest in technology flourished.
                    </p>
                    <p>
                        I first started to take interest in programming during high school in our ICT class.
                        We were introduced to Minecraft and played a mini-game involving building literal blocks
                        of code to make the character Steve move around.
                    </p>
                    <p>
                        Since then, I've been engaged beyond measure—playing games, learning software, and applying
                        what I've learned to create projects. In college, I found my true calling, and I'm now
                        on my way to becoming a full-stack developer.
                    </p>
                </div>
            </div>

            <div class="contact-section">
                <div class="social-icons">
                    <a href="https://www.linkedin.com/in/miguel-antonio-tan-945499255/" aria-label="LinkedIn Profile">
                        <i class="fa-brands fa-linkedin"></i>
                    </a>
                    <a href="https://github.com/leugimnat" aria-label="GitHub Profile">
                        <i class="fa-brands fa-github"></i>
                    </a>
                    <a href="https://www.facebook.com/zeuqzavleugimnat/" aria-label="Facebook Profile">
                        <i class="fa-brands fa-facebook"></i>
                    </a>
                </div>
                <a href="#contact" class="btn">Hire Me</a>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact">
        <div class="contact-container">
            <div class="contact-header">
                <h2>Get In <span>Touch</span></h2>
                <p>Have a project in mind or want to collaborate? I'd love to hear from you!</p>
            </div>

            <div class="contact-content">
                <div class="contact-info">
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fa-solid fa-envelope"></i>
                        </div>
                        <div class="contact-details">
                            <h3>Email</h3>
                            <p><EMAIL></p>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fa-solid fa-location-dot"></i>
                        </div>
                        <div class="contact-details">
                            <h3>Location</h3>
                            <p>Cagayan de Oro City, Philippines</p>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fa-solid fa-clock"></i>
                        </div>
                        <div class="contact-details">
                            <h3>Response Time</h3>
                            <p>Usually within 24 hours</p>
                        </div>
                    </div>

                    <div class="contact-social">
                        <h3>Connect with me</h3>
                        <div class="social-icons">
                            <a href="https://www.linkedin.com/in/miguel-antonio-tan-945499255/" aria-label="LinkedIn Profile">
                                <i class="fa-brands fa-linkedin"></i>
                            </a>
                            <a href="https://github.com/leugimnat" aria-label="GitHub Profile">
                                <i class="fa-brands fa-github"></i>
                            </a>
                            <a href="https://www.facebook.com/zeuqzavleugimnat/" aria-label="Facebook Profile">
                                <i class="fa-brands fa-facebook"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="contact-form-container">
                    <form id="contact-form" class="contact-form">
                        <div class="form-group">
                            <label for="name">Full Name *</label>
                            <input type="text" id="name" name="name" required>
                        </div>

                        <div class="form-group">
                            <label for="email">Email Address *</label>
                            <input type="email" id="email" name="email" required>
                        </div>

                        <div class="form-group">
                            <label for="subject">Subject *</label>
                            <select id="subject" name="subject" required>
                                <option value="">Select a subject</option>
                                <option value="job-opportunity">Job Opportunity</option>
                                <option value="project-collaboration">Project Collaboration</option>
                                <option value="freelance-work">Freelance Work</option>
                                <option value="general-inquiry">General Inquiry</option>
                                <option value="other">Other</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="message">Message *</label>
                            <textarea id="message" name="message" rows="6" required placeholder="Tell me about your project or inquiry..."></textarea>
                        </div>

                        <button type="submit" class="submit-btn">
                            <span class="btn-text">Send Message</span>
                            <span class="btn-loading">
                                <i class="fa-solid fa-spinner fa-spin"></i> Sending...
                            </span>
                        </button>
                    </form>

                    <div id="form-status" class="form-status"></div>
                </div>
            </div>
        </div>
    </section>
</body>
<footer class="site-footer">
  <div class="footer-inner">
    <div class="footer-main">
      <div class="footer-brand">
        <h3 class="footer-logo">@leugimnat</h3>
        <p class="footer-name">&copy; <span id="year"></span> Miguel Antonio V Tan</p>
        <p class="footer-status">
          <span class="status-indicator"></span>
          Open to Front-End / Full-Stack Roles / Internship Opportunities
        </p>
      </div>

      <div class="footer-links">
        <div class="footer-contact">
          <h4>Get In Touch</h4>
          <div class="contact-info">
            <a href="mailto:<EMAIL>" class="contact-email">
              <i class="fa-solid fa-envelope"></i>
              <EMAIL>
            </a>
            <div class="contact-location">
              <i class="fa-solid fa-location-dot"></i>
              <span>Cagayan de Oro City, Philippines</span>
            </div>
          </div>
        </div>

        <div class="footer-social">
          <h4>Connect</h4>
          <div class="social-links">
            <a href="https://github.com/leugimnat" aria-label="GitHub Profile" title="GitHub">
              <i class="fa-brands fa-github"></i>
              <span>GitHub</span>
            </a>
            <a href="https://www.linkedin.com/in/miguel-antonio-tan-945499255/" aria-label="LinkedIn Profile" title="LinkedIn">
              <i class="fa-brands fa-linkedin"></i>
              <span>LinkedIn</span>
            </a>
            <a href="https://www.facebook.com/zeuqzavleugimnat/" aria-label="Facebook Profile" title="Facebook">
              <i class="fa-brands fa-facebook"></i>
              <span>Facebook</span>
            </a>
          </div>
        </div>
      </div>
    </div>

    <div class="footer-bottom">
      <div class="footer-tech">
        <span class="tech-label">Tech Stack:</span>
        <div class="tech-stack">
          <span class="tech-item">HTML</span>
          <span class="tech-item">CSS</span>
          <span class="tech-item">JavaScript</span>
        </div>
      </div>
      <p class="footer-note">
        <i class="fa-solid fa-heart"></i>
        Crafted with care • Accessible & responsive • Last updated Aug 2025
      </p>
    </div>
  </div>
</footer>
<script>
  // Update year
  document.getElementById('year').textContent = new Date().getFullYear();

  // Mobile menu functionality
  const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
  const navMenu = document.querySelector('.nav-menu');
  const navLinks = document.querySelectorAll('.nav-link');
  const navbar = document.querySelector('.navbar');

  // Toggle mobile menu
  mobileMenuToggle.addEventListener('click', () => {
    mobileMenuToggle.classList.toggle('active');
    navMenu.classList.toggle('active');

    // Update aria-expanded attribute
    const isExpanded = navMenu.classList.contains('active');
    mobileMenuToggle.setAttribute('aria-expanded', isExpanded);
  });

  // Close mobile menu when clicking on nav links
  navLinks.forEach(link => {
    link.addEventListener('click', () => {
      mobileMenuToggle.classList.remove('active');
      navMenu.classList.remove('active');
      mobileMenuToggle.setAttribute('aria-expanded', 'false');
    });
  });

  // Close mobile menu when clicking outside
  document.addEventListener('click', (e) => {
    if (!navMenu.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
      mobileMenuToggle.classList.remove('active');
      navMenu.classList.remove('active');
      mobileMenuToggle.setAttribute('aria-expanded', 'false');
    }
  });

  // Navbar scroll effect
  window.addEventListener('scroll', () => {
    if (window.scrollY > 100) {
      navbar.classList.add('scrolled');
    } else {
      navbar.classList.remove('scrolled');
    }
  });

  // Smooth scrolling for navigation links
  navLinks.forEach(link => {
    link.addEventListener('click', (e) => {
      const href = link.getAttribute('href');
      if (href.startsWith('#')) {
        e.preventDefault();
        const targetId = href.substring(1);
        const targetElement = document.getElementById(targetId);

        if (targetElement) {
          const offsetTop = targetElement.offsetTop - 80; // Account for fixed navbar
          window.scrollTo({
            top: offsetTop,
            behavior: 'smooth'
          });

          // Update active link
          navLinks.forEach(l => l.classList.remove('active'));
          link.classList.add('active');
        }
      }
    });
  });

  // Update active link on scroll
  window.addEventListener('scroll', () => {
    const sections = document.querySelectorAll('section[id]');
    const scrollPos = window.scrollY + 100;

    sections.forEach(section => {
      const sectionTop = section.offsetTop;
      const sectionHeight = section.offsetHeight;
      const sectionId = section.getAttribute('id');

      if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
        navLinks.forEach(link => {
          link.classList.remove('active');
          if (link.getAttribute('href') === `#${sectionId}`) {
            link.classList.add('active');
          }
        });
      }
    });
  });

  // Contact Form Functionality
  const contactForm = document.getElementById('contact-form');
  const formStatus = document.getElementById('form-status');
  const submitBtn = contactForm.querySelector('.submit-btn');
  const btnText = submitBtn.querySelector('.btn-text');
  const btnLoading = submitBtn.querySelector('.btn-loading');

  // Initialize EmailJS (you'll need to replace with your actual EmailJS credentials)
  // emailjs.init("YOUR_PUBLIC_KEY"); // Uncomment and add your EmailJS public key

  contactForm.addEventListener('submit', async (e) => {
    e.preventDefault();

    // Get form data
    const formData = new FormData(contactForm);
    const name = formData.get('name');
    const email = formData.get('email');
    const subject = formData.get('subject');
    const message = formData.get('message');

    // Basic validation
    if (!name || !email || !subject || !message) {
      showFormStatus('Please fill in all required fields.', 'error');
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      showFormStatus('Please enter a valid email address.', 'error');
      return;
    }

    // Show loading state
    setLoadingState(true);

    try {
      // Using Formspree (replace with your Formspree endpoint)
      const response = await fetch('https://formspree.io/f/mvgqljgn', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: name,
          email: email,
          subject: subject,
          message: message
        })
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      // For demo purposes - simulate successful submission
      await new Promise(resolve => setTimeout(resolve, 2000));

      showFormStatus('Thank you! Your message has been sent successfully. I\'ll get back to you soon!', 'success');
      contactForm.reset();

    } catch (error) {
      console.error('Error sending message:', error);
      showFormStatus('Sorry, there was an error sending your message. Please try again or contact me directly via email.', 'error');
    } finally {
      setLoadingState(false);
    }
  });

  function setLoadingState(loading) {
    if (loading) {
      submitBtn.disabled = true;
      btnText.style.display = 'none';
      btnLoading.style.display = 'inline-flex';
    } else {
      submitBtn.disabled = false;
      btnText.style.display = 'inline';
      btnLoading.style.display = 'none';
    }
  }

  function showFormStatus(message, type) {
    formStatus.textContent = message;
    formStatus.className = `form-status ${type}`;
    formStatus.style.display = 'block';

    // Auto-hide success messages after 5 seconds
    if (type === 'success') {
      setTimeout(() => {
        formStatus.style.display = 'none';
      }, 5000);
    }
  }
</script>
</html>